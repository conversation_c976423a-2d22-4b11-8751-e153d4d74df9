import os
import requests
from datetime import datetime
from dotenv import load_dotenv
from typing import Any, Dict, List
from utils.embed_utils import BaseMagentoClient

load_dotenv()


class DGEClient:
    """Client for interacting with DGE API for product data."""

    def __init__(self):
        self.base_url = os.environ.get("DGE_API_URL")
        self.download_url = os.environ.get("DGE_DOWNLOAD_URL")
        self.username = os.environ.get("DGE_USERNAME")
        self.password = os.environ.get("DGE_PASSWORD")
        self.auth_key = None
        self.dge_token = None
        self.uid = None
        self.authenticate()
        self.authenticate_dge()

    def authenticate(self) -> bool:
        """Authenticate with DGE API and get tokens."""
        endpoint = f"{self.base_url}/v1/tokens"
        headers = {"content-type": "application/json"}
        payload = {
            "auth": {
                "methods": "password",
                "password": {
                    "user": {"username": self.username, "password": self.password}
                },
            }
        }

        try:
            response = requests.post(endpoint, headers=headers, json=payload)
            response.raise_for_status()
            data = response.json()

            print("++++++++++++++++++++++++++++ :", data)

            self.auth_key = data.get("token", {}).get("auth_key")
            print("++++++++++++++++++++++++++++ :", self.auth_key)
            self.uid = data.get("services", [{}])[0].get("UID")
            print("++++++++++++++++++++++++++++ :", self.uid)

            if not self.auth_key or not self.uid:
                print("[ERROR] Failed to extract auth tokens from response")
                return False

            print(f"[AUTH] Successfully authenticated with DGE API")
            return True

        except Exception as e:
            print(f"[ERROR] Authentication failed: {str(e)}")
            return False

    def authenticate_dge(self) -> bool:
        endpoint = f"{self.download_url}/v1/dge/v1/tokens"
        headers = {
            "x-auth-token": self.auth_key,
            "x-service-token": self.uid,
            "application-context": "TESS",
            "Content-Type": "application/json",
        }
        payload = {
            "username": os.environ.get("DGE_API_USERNAME"),
            "password": os.environ.get("DGE_API_PASSWORD"),
            "appKey": os.environ.get("DGE_API_APP_KEY"),
            "deviceId": "TESS",
        }

        print("++++++++++++++++++++++++++++ :", payload)
        print("++++++++++++++++++++++++++++ :", headers)

        try:
            response = requests.post(endpoint, headers=headers, json=payload)
            response.raise_for_status()
            data = response.json()

            print("++++++++++++++++++++++++++++ :", data)

            self.dge_token = data.get("id", "")
            print("++++++++++++++++++++++++++++ :", self.dge_token)

            if not self.dge_token:
                print("[ERROR] Failed to extract auth tokens from response")
                return False

            print(f"[AUTH] Successfully authenticated with DGE API")
            return True

        except Exception as e:
            print(f"[ERROR] Authentication failed: {str(e)}")
            return False

    def fetch_sales_orders(self) -> List[Dict[str, Any]]:
        if not self.dge_token:
            if not self.authenticate_dge():
                raise Exception("Authentication required before fetching sales orders")

        endpoint = f"{self.download_url}/v1/dge/v1/salesorders"
        headers = {
            "x-auth-token": self.auth_key,
            "x-service-token": self.uid,
            "application-context": "TESS",
            "Content-Type": "application/json",
            "x-dge-token": self.dge_token,
        }

        try:
            response = requests.get(endpoint, headers=headers)
            response.raise_for_status()
            data = response.json()

            print("++++++++++++++++++++++++++++ :", data)

            return data

        except Exception as e:
            print(f"[ERROR] Failed to fetch sales orders: {str(e)}")
            return []


def fetch_dge_orders_task(**context):
    """Airflow task to fetch sales orders from DGE API."""
    client = DGEClient()
    try:
        orders = client.fetch_sales_orders()
        print(f"[FETCH] Fetched {len(orders)} sales orders from DGE API")
        context["task_instance"].xcom_push(key="orders", value={"orders": orders})
        return {"orders": orders}
    except Exception as e:
        print(f"[ERROR] Failed to fetch sales orders: {str(e)}")
        raise
